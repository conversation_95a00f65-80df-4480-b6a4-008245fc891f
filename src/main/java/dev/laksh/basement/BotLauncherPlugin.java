package dev.laksh.basement;

import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitRunnable;
import java.io.File;
import java.io.IOException;
import java.io.BufferedReader;
import java.io.InputStreamReader;

public class BotLauncherPlugin extends JavaPlugin {

    private Process botProcess;
    private boolean botEnabled = true;

    @Override
    public void onEnable() {
        // Save default config if it doesn't exist
        saveDefaultConfig();
        
        // Load config values
        botEnabled = getConfig().getBoolean("bot.enabled", true);
        String botDirectory = getConfig().getString("bot.directory", "discord-bot");
        String botScript = getConfig().getString("bot.script", "index.js");
        
        if (!botEnabled) {
            getLogger().info("Discord bot is disabled in config. Skipping startup.");
            return;
        }

        getLogger().info("Starting Discord bot...");
        
        // Determine bot directory path
        File botDir;
        if (new File(botDirectory).isAbsolute()) {
            botDir = new File(botDirectory);
        } else {
            // Relative to server root (parent of plugins folder)
            botDir = new File(getDataFolder().getParentFile().getParentFile(), botDirectory);
        }

        if (!botDir.exists()) {
            getLogger().warning("Discord bot directory not found at: " + botDir.getAbsolutePath());
            getLogger().warning("Please create the directory and place your bot files there.");
            return;
        }

        File botFile = new File(botDir, botScript);
        if (!botFile.exists()) {
            getLogger().warning("Bot script not found at: " + botFile.getAbsolutePath());
            getLogger().warning("Please ensure " + botScript + " exists in the bot directory.");
            return;
        }

        startBot(botDir, botScript);
    }

    private void startBot(File botDir, String botScript) {
        try {
            // Try to find Node.js executable
            String nodeCommand = findNodeExecutable();
            if (nodeCommand == null) {
                getLogger().severe("Node.js not found! Please install Node.js or configure the path.");
                return;
            }

            getLogger().info("Using Node.js at: " + nodeCommand);
            ProcessBuilder builder = new ProcessBuilder(nodeCommand, botScript);
            builder.directory(botDir);

            // Redirect output to server console with prefix
            builder.redirectErrorStream(true);

            botProcess = builder.start();
            
            // Monitor bot output in a separate thread
            new BukkitRunnable() {
                @Override
                public void run() {
                    try (BufferedReader reader = new BufferedReader(
                            new InputStreamReader(botProcess.getInputStream()))) {
                        String line;
                        while ((line = reader.readLine()) != null && !isCancelled()) {
                            getLogger().info("[Discord Bot] " + line);
                        }
                    } catch (IOException e) {
                        if (!isCancelled()) {
                            getLogger().warning("Error reading bot output: " + e.getMessage());
                        }
                    }
                }
            }.runTaskAsynchronously(this);
            
            // Check if bot started successfully
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (botProcess != null && botProcess.isAlive()) {
                        getLogger().info("Discord bot launched successfully!");
                    } else {
                        getLogger().severe("Discord bot failed to start or crashed immediately.");
                    }
                }
            }.runTaskLater(this, 40L); // Check after 2 seconds
            
        } catch (IOException e) {
            getLogger().severe("Failed to start Discord bot: " + e.getMessage());
            getLogger().severe("Make sure Node.js is installed and accessible from command line.");
        }
    }

    @Override
    public void onDisable() {
        if (botProcess != null && botProcess.isAlive()) {
            getLogger().info("Stopping Discord bot...");
            
            // Try graceful shutdown first
            botProcess.destroy();
            
            // Wait a bit for graceful shutdown
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // Force kill if still running
            if (botProcess.isAlive()) {
                getLogger().warning("Bot didn't stop gracefully, forcing shutdown...");
                botProcess.destroyForcibly();
            }
            
            getLogger().info("Discord bot stopped.");
        }
    }
    
    // Command to restart the bot (optional feature)
    @Override
    public boolean onCommand(org.bukkit.command.CommandSender sender, 
                           org.bukkit.command.Command command, 
                           String label, String[] args) {
        if (command.getName().equalsIgnoreCase("restartbot")) {
            if (!sender.hasPermission("basementbot.restart")) {
                sender.sendMessage("§cYou don't have permission to restart the bot.");
                return true;
            }
            
            sender.sendMessage("§eRestarting Discord bot...");
            
            // Stop current bot
            if (botProcess != null && botProcess.isAlive()) {
                botProcess.destroy();
            }
            
            // Restart after a delay
            new BukkitRunnable() {
                @Override
                public void run() {
                    onEnable();
                    sender.sendMessage("§aDiscord bot restarted!");
                }
            }.runTaskLater(this, 60L); // 3 second delay
            
            return true;
        }
        return false;
    }

    /**
     * Try to find Node.js executable in common locations
     */
    private String findNodeExecutable() {
        // First check if it's configured in config
        String configuredPath = getConfig().getString("bot.node-path", "");
        if (!configuredPath.isEmpty()) {
            File nodeFile = new File(configuredPath);
            if (nodeFile.exists() && nodeFile.canExecute()) {
                return configuredPath;
            }
        }

        // Common Node.js locations to check
        String[] possiblePaths = {
            "node",                    // Try PATH first
            "/usr/bin/node",          // Common Linux location
            "/usr/local/bin/node",    // Common macOS/Linux location
            "/opt/homebrew/bin/node", // macOS Homebrew ARM
            "/home/<USER>/.linuxbrew/bin/node", // Linux Homebrew
            System.getProperty("user.home") + "/.nvm/versions/node/*/bin/node", // NVM
            "/snap/bin/node",         // Snap package
            "C:\\Program Files\\nodejs\\node.exe",     // Windows
            "C:\\Program Files (x86)\\nodejs\\node.exe" // Windows 32-bit
        };

        for (String path : possiblePaths) {
            if (path.equals("node")) {
                // Test if 'node' is available in PATH
                try {
                    Process testProcess = new ProcessBuilder("node", "--version").start();
                    testProcess.waitFor();
                    if (testProcess.exitValue() == 0) {
                        return "node";
                    }
                } catch (Exception e) {
                    // Continue to next path
                }
            } else if (path.contains("*")) {
                // Handle NVM path with wildcard
                try {
                    String nvmBase = path.substring(0, path.indexOf("*"));
                    File nvmDir = new File(nvmBase).getParentFile().getParentFile();
                    if (nvmDir.exists()) {
                        File[] versionDirs = nvmDir.listFiles();
                        if (versionDirs != null) {
                            for (File versionDir : versionDirs) {
                                if (versionDir.isDirectory() && versionDir.getName().startsWith("v")) {
                                    File nodeExe = new File(versionDir, "bin/node");
                                    if (nodeExe.exists() && nodeExe.canExecute()) {
                                        return nodeExe.getAbsolutePath();
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    // Continue to next path
                }
            } else {
                // Check regular path
                File nodeFile = new File(path);
                if (nodeFile.exists() && nodeFile.canExecute()) {
                    return path;
                }
            }
        }

        return null; // Node.js not found
    }
}
