package dev.laksh.basement;

import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitRunnable;
import java.io.File;
import java.io.IOException;
import java.io.BufferedReader;
import java.io.InputStreamReader;

public class BotLauncherPlugin extends JavaPlugin {

    private Process botProcess;
    private boolean botEnabled = true;

    @Override
    public void onEnable() {
        // Save default config if it doesn't exist
        saveDefaultConfig();
        
        // Load config values
        botEnabled = getConfig().getBoolean("bot.enabled", true);
        String botDirectory = getConfig().getString("bot.directory", "discord-bot");
        String botScript = getConfig().getString("bot.script", "index.js");
        
        if (!botEnabled) {
            getLogger().info("Discord bot is disabled in config. Skipping startup.");
            return;
        }

        getLogger().info("Starting Discord bot...");
        
        // Determine bot directory path
        File botDir;
        if (new File(botDirectory).isAbsolute()) {
            botDir = new File(botDirectory);
        } else {
            // Relative to server root (parent of plugins folder)
            botDir = new File(getDataFolder().getParentFile().getParentFile(), botDirectory);
        }

        if (!botDir.exists()) {
            getLogger().warning("Discord bot directory not found at: " + botDir.getAbsolutePath());
            getLogger().warning("Please create the directory and place your bot files there.");
            return;
        }

        File botFile = new File(botDir, botScript);
        if (!botFile.exists()) {
            getLogger().warning("Bot script not found at: " + botFile.getAbsolutePath());
            getLogger().warning("Please ensure " + botScript + " exists in the bot directory.");
            return;
        }

        startBot(botDir, botScript);
    }

    private void startBot(File botDir, String botScript) {
        try {
            ProcessBuilder builder = new ProcessBuilder("node", botScript);
            builder.directory(botDir);
            
            // Redirect output to server console with prefix
            builder.redirectErrorStream(true);
            
            botProcess = builder.start();
            
            // Monitor bot output in a separate thread
            new BukkitRunnable() {
                @Override
                public void run() {
                    try (BufferedReader reader = new BufferedReader(
                            new InputStreamReader(botProcess.getInputStream()))) {
                        String line;
                        while ((line = reader.readLine()) != null && !isCancelled()) {
                            getLogger().info("[Discord Bot] " + line);
                        }
                    } catch (IOException e) {
                        if (!isCancelled()) {
                            getLogger().warning("Error reading bot output: " + e.getMessage());
                        }
                    }
                }
            }.runTaskAsynchronously(this);
            
            // Check if bot started successfully
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (botProcess != null && botProcess.isAlive()) {
                        getLogger().info("Discord bot launched successfully!");
                    } else {
                        getLogger().severe("Discord bot failed to start or crashed immediately.");
                    }
                }
            }.runTaskLater(this, 40L); // Check after 2 seconds
            
        } catch (IOException e) {
            getLogger().severe("Failed to start Discord bot: " + e.getMessage());
            getLogger().severe("Make sure Node.js is installed and accessible from command line.");
        }
    }

    @Override
    public void onDisable() {
        if (botProcess != null && botProcess.isAlive()) {
            getLogger().info("Stopping Discord bot...");
            
            // Try graceful shutdown first
            botProcess.destroy();
            
            // Wait a bit for graceful shutdown
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // Force kill if still running
            if (botProcess.isAlive()) {
                getLogger().warning("Bot didn't stop gracefully, forcing shutdown...");
                botProcess.destroyForcibly();
            }
            
            getLogger().info("Discord bot stopped.");
        }
    }
    
    // Command to restart the bot (optional feature)
    @Override
    public boolean onCommand(org.bukkit.command.CommandSender sender, 
                           org.bukkit.command.Command command, 
                           String label, String[] args) {
        if (command.getName().equalsIgnoreCase("restartbot")) {
            if (!sender.hasPermission("basementbot.restart")) {
                sender.sendMessage("§cYou don't have permission to restart the bot.");
                return true;
            }
            
            sender.sendMessage("§eRestarting Discord bot...");
            
            // Stop current bot
            if (botProcess != null && botProcess.isAlive()) {
                botProcess.destroy();
            }
            
            // Restart after a delay
            new BukkitRunnable() {
                @Override
                public void run() {
                    onEnable();
                    sender.sendMessage("§aDiscord bot restarted!");
                }
            }.runTaskLater(this, 60L); // 3 second delay
            
            return true;
        }
        return false;
    }
}
