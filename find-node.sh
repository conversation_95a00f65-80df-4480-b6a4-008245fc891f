#!/bin/bash

echo "🔍 Finding Node.js installation..."
echo "=================================="

# Check if node is in PATH
if command -v node >/dev/null 2>&1; then
    echo "✅ Node.js found in PATH:"
    echo "   Command: node"
    echo "   Location: $(which node)"
    echo "   Version: $(node --version)"
    echo ""
else
    echo "❌ Node.js not found in PATH"
    echo ""
fi

# Check common locations
echo "🔍 Checking common Node.js locations:"
echo "======================================"

locations=(
    "/usr/bin/node"
    "/usr/local/bin/node"
    "/opt/homebrew/bin/node"
    "/home/<USER>/.linuxbrew/bin/node"
    "/snap/bin/node"
    "$HOME/.nvm/versions/node/*/bin/node"
)

for location in "${locations[@]}"; do
    if [[ "$location" == *"*"* ]]; then
        # Handle wildcard paths (like NVM)
        for file in $location; do
            if [[ -f "$file" && -x "$file" ]]; then
                echo "✅ Found: $file"
                echo "   Version: $($file --version 2>/dev/null || echo 'Unknown')"
            fi
        done
    else
        if [[ -f "$location" && -x "$location" ]]; then
            echo "✅ Found: $location"
            echo "   Version: $($location --version 2>/dev/null || echo 'Unknown')"
        else
            echo "❌ Not found: $location"
        fi
    fi
done

echo ""
echo "💡 Instructions:"
echo "================"
echo "1. Copy the path of a working Node.js installation above"
echo "2. Edit your plugin config at: plugins/BasementBotLauncher/config.yml"
echo "3. Set the 'node-path' value to the full path"
echo ""
echo "Example config:"
echo "bot:"
echo "  node-path: \"/usr/bin/node\""
echo ""
echo "Or leave it empty for auto-detection (recommended if Node.js is in PATH)"
