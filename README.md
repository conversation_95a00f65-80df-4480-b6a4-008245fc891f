# BasementBotLauncher

A Bukkit plugin that automatically launches and manages your Discord bot when your Minecraft server starts.

## Features

- 🚀 **Auto-start**: Launches your Discord bot when the server starts
- 🛑 **Auto-stop**: Gracefully stops the bot when the server shuts down
- 📝 **Console Integration**: Bot output appears in server console with `[Discord Bot]` prefix
- ⚙️ **Configurable**: Customize bot directory and script name
- 🔄 **Restart Command**: `/restartbot` command for operators
- 🛡️ **Error Handling**: Proper error messages and fallback handling

## Installation

1. **Build the plugin**:
   ```bash
   mvn clean package
   ```

2. **Copy the JAR**: Place `target/basement-bot-launcher-1.0.0.jar` in your server's `plugins/` folder

3. **Create bot directory**: Create a `discord-bot/` folder in your server root (same level as `plugins/`)

4. **Add your bot**: Place your Discord bot files (including `index.js`) in the `discord-bot/` folder

## Directory Structure

```
your-server/
├── plugins/
│   └── basement-bot-launcher-1.0.0.jar
├── discord-bot/
│   ├── index.js          # Your main bot file
│   ├── package.json
│   ├── node_modules/
│   └── ...               # Other bot files
└── ...
```

## Configuration

The plugin creates a `config.yml` in `plugins/BasementBotLauncher/`:

```yaml
bot:
  enabled: true              # Enable/disable bot startup
  directory: "discord-bot"   # Bot directory (relative to server root)
  script: "index.js"         # Main bot script file
```

## Commands

- `/restartbot` - Restart the Discord bot (requires `basementbot.restart` permission)

## Permissions

- `basementbot.restart` - Allows using `/restartbot` command (default: op)

## Requirements

- **Java 8+**
- **Node.js** installed and accessible from command line
- **Bukkit/Spigot/Paper** server (API version 1.13+)
- Your Discord bot must be properly configured with tokens, etc.

## Troubleshooting

### Bot doesn't start - "No such file or directory" error
This usually means the plugin can't find Node.js. Here's how to fix it:

1. **Find your Node.js path** by running the included script:
   ```bash
   ./find-node.sh
   ```

2. **Configure the path** in `plugins/BasementBotLauncher/config.yml`:
   ```yaml
   bot:
     node-path: "/usr/bin/node"  # Use the path you found
   ```

3. **Common Node.js locations:**
   - `/usr/bin/node` (most Linux systems)
   - `/usr/local/bin/node` (macOS/Linux)
   - `/opt/homebrew/bin/node` (macOS Homebrew ARM)
   - `/home/<USER>/.linuxbrew/bin/node` (Linux Homebrew)

### Bot doesn't start - Other reasons
- Check that Node.js is installed: `node --version`
- Verify your bot directory exists and contains `index.js`
- Check server console for error messages
- Ensure your bot's `package.json` dependencies are installed (`npm install`)

### Bot crashes immediately
- Test your bot manually: `cd discord-bot && node index.js`
- Check for missing environment variables or configuration
- Verify Discord bot token is valid

### Permission denied
- Make sure the server has permission to execute Node.js
- On Linux/Mac, you might need to make files executable

### For Pterodactyl/Docker environments
If you're running in a containerized environment:
- The Node.js path might be different inside the container
- Try setting `node-path: "node"` if Node.js is in the container's PATH
- Consider running your Discord bot as a separate service/container

## Development

To modify or extend this plugin:

1. Clone/download the source code
2. Import into your IDE as a Maven project
3. Make your changes
4. Build with `mvn clean package`

## License

This project is open source. Feel free to modify and distribute as needed.
