# BasementBotLauncher Configuration

bot:
  # Whether to start the Discord bot when the server starts
  enabled: true

  # Directory containing the Discord bot files
  # Can be absolute path or relative to server root
  directory: "discord-bot"

  # The main script file to run (usually index.js)
  script: "index.js"

  # Path to Node.js executable (leave empty for auto-detection)
  # Examples: "/usr/bin/node", "/usr/local/bin/node", "C:\\Program Files\\nodejs\\node.exe"
  node-path: ""
